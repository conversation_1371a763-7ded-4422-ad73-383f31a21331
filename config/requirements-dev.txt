# ============================================================================
# Kitco Research AI - Development Requirements
# ============================================================================
# Additional dependencies for development, testing, and code quality.
# These are not required for production deployment.
#
# Installation: pip install -r config/requirements-dev.txt
# ============================================================================

# Include all production requirements
-r requirements.txt

# ============================================================================
# TESTING & TEST UTILITIES
# ============================================================================
pytest==8.3.5                   # Testing framework (already in main requirements)
pytest-cov==6.0.0               # Coverage reporting for pytest
pytest-mock==3.14.0             # Mock utilities for pytest
pytest-asyncio==0.25.0          # Async testing support
pytest-xdist==3.6.1             # Parallel test execution
pytest-html==4.1.1              # HTML test reports
pytest-benchmark==5.1.0         # Performance benchmarking
coverage==7.6.9                 # Code coverage measurement
factory-boy==3.3.1              # Test data generation

# ============================================================================
# CODE QUALITY & LINTING
# ============================================================================
# Linting & Formatting
black==24.10.0                  # Code formatter
isort==5.13.2                   # Import sorting
flake8==7.1.1                   # Style guide enforcement
pylint==3.3.1                   # Static code analysis
autopep8==2.3.0                 # Automatic PEP 8 formatting

# Type Checking
mypy==1.13.0                    # Static type checker
types-requests==2.32.0.20241016 # Type stubs for requests
types-PyYAML==6.0.12.20240917   # Type stubs for PyYAML
types-python-dateutil==2.9.0.20241003  # Type stubs for python-dateutil

# Code Quality Tools
bandit==1.8.0                   # Security linter
safety==3.2.11                  # Dependency vulnerability scanner
pre-commit==4.0.1               # Git pre-commit hooks
commitizen==3.29.1              # Conventional commits

# ============================================================================
# DOCUMENTATION
# ============================================================================
sphinx==8.1.3                   # Documentation generator
sphinx-rtd-theme==3.0.2         # Read the Docs theme
sphinx-autodoc-typehints==2.4.4 # Type hints in documentation
myst-parser==4.0.0              # Markdown parser for Sphinx
mkdocs==1.6.1                   # Alternative documentation generator
mkdocs-material==9.5.44         # Material theme for MkDocs

# ============================================================================
# DEVELOPMENT UTILITIES
# ============================================================================
# Debugging & Profiling
ipdb==0.13.13                   # Enhanced debugger
pdbpp==0.10.3                   # Enhanced pdb
line-profiler==4.1.3            # Line-by-line profiling
memory-profiler==0.61.0         # Memory usage profiling
py-spy==0.4.0                   # Sampling profiler

# Development Tools
ipython==8.30.0                 # Enhanced interactive Python shell
jupyter==1.1.1                  # Jupyter notebook
notebook==7.3.1                 # Jupyter notebook server
jupyterlab==4.3.3               # JupyterLab interface

# Environment & Dependency Management
pip-tools==7.4.1                # Dependency management
pipdeptree==2.23.4              # Dependency tree visualization
pip-audit==2.7.3                # Security audit for dependencies

# ============================================================================
# PERFORMANCE & MONITORING
# ============================================================================
# Performance Testing
locust==2.32.4                  # Load testing framework
pytest-benchmark==5.1.0         # Performance benchmarking (duplicate, but important)

# Monitoring & Observability
prometheus-client==0.21.1       # Prometheus metrics
structlog==24.4.0               # Structured logging

# ============================================================================
# DATABASE DEVELOPMENT
# ============================================================================
# Database Tools
alembic==1.16.1                 # Database migrations (already in main requirements)
sqlalchemy-utils==0.41.2        # SQLAlchemy utilities
faker==33.1.0                   # Fake data generation

# ============================================================================
# API DEVELOPMENT & TESTING
# ============================================================================
# API Testing
httpx==0.28.1                   # HTTP client (already in main requirements)
responses==0.25.3               # Mock HTTP responses
requests-mock==1.12.1           # Mock requests library
vcrpy==6.0.2                    # Record and replay HTTP interactions

# API Documentation
flask-restx==1.3.0              # Flask REST API framework with Swagger
apispec==6.7.1                  # API specification generator

# ============================================================================
# FRONTEND DEVELOPMENT (if needed)
# ============================================================================
# Node.js tools (if using npm/yarn for frontend assets)
# Note: These would typically be installed via npm/yarn, not pip
# nodeenv==1.8.0                # Node.js virtual environment

# ============================================================================
# CONTAINERIZATION & DEPLOYMENT
# ============================================================================
# Docker utilities
docker==7.1.0                   # Docker SDK for Python
# docker-compose==1.29.2        # Docker Compose (deprecated, use `docker compose` CLI instead)

# ============================================================================
# SECURITY & COMPLIANCE
# ============================================================================
# Security scanning
semgrep==1.95.0                 # Static analysis security scanner
pip-audit==2.7.3                # Dependency vulnerability scanner (duplicate)

# ============================================================================
# MISCELLANEOUS DEVELOPMENT TOOLS
# ============================================================================
# File watching & auto-reload
watchdog==6.0.0                 # File system event monitoring
python-dotenv==1.1.0            # Environment variables (already in main requirements)

# Data analysis & exploration
# ydata-profiling==4.16.1         # Automated EDA reports (formerly pandas-profiling) - NOT COMPATIBLE WITH PYTHON 3.13
seaborn==0.13.2                 # Statistical data visualization

# ============================================================================
# OPTIONAL: ADDITIONAL AI/ML DEVELOPMENT TOOLS
# ============================================================================
# Experiment tracking
wandb==0.18.7                   # Weights & Biases experiment tracking
mlflow==2.19.0                  # ML lifecycle management

# Model serving
fastapi==0.115.6                # Fast API framework (alternative to Flask)
uvicorn==0.32.1                 # ASGI server

# ============================================================================
# NOTES
# ============================================================================
# To install all development dependencies:
# pip install -r config/requirements-dev.txt
#
# To install only production dependencies:
# pip install -r config/requirements.txt
#
# To update this file after adding new dev dependencies:
# pip freeze | grep -E "(pytest|black|mypy|sphinx)" >> config/requirements-dev.txt
# (then manually organize and clean up)
