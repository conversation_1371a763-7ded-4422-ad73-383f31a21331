#!/usr/bin/env python3
"""
Kitco Research AI - Dependency Validator and Auto-Fixer
========================================================
This script validates and automatically fixes dependency issues to prevent setup failures.
It ensures all requirements files are consistent and compatible across all platforms.
"""

import os
import sys
import re
import subprocess
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
import tempfile


@dataclass
class PackageInfo:
    """Information about a package dependency"""
    name: str
    version: str
    original_line: str
    line_number: int
    file_path: str
    comment: str = ""


@dataclass
class ValidationResult:
    """Result of dependency validation"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    fixes_applied: List[str]


class DependencyValidator:
    """Validates and fixes dependency issues across all requirements files"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.requirements_files = [
            "config/requirements.txt",
            "config/requirements-dev.txt", 
            "config/requirements-prod.txt"
        ]
        
        # Known package name corrections
        self.package_corrections = {
            "vcr.py": "vcrpy",
            "pandas-profiling": "ydata-profiling",
            "docker-compose": None,  # Remove deprecated package
        }
        
        # Known version compatibility constraints
        self.compatibility_constraints = {
            "safety": {
                "filelock": "~=3.12.2",
                "psutil": "~=6.0.0",
            },
            "ydata-profiling": {
                "matplotlib": "<=3.10",
                "numpy": "<2.2",
            },
            "safety-schemas": {
                "pydantic": "<2.10.0",
            },
            "unstructured-client": {
                "pydantic": ">=2.9.0,<2.10.0",
            }
        }
        
        # Packages that should be consistent across all files
        self.core_packages = {
            "Flask", "langchain", "openai", "numpy", "pandas", "pydantic",
            "requests", "sqlalchemy", "playwright", "beautifulsoup4"
        }

    def validate_all(self) -> ValidationResult:
        """Validate all requirements files and fix issues"""
        print("🔍 Validating dependency files...")
        
        result = ValidationResult(True, [], [], [])
        
        # Step 1: Validate individual files
        for req_file in self.requirements_files:
            file_path = self.project_root / req_file
            if file_path.exists():
                file_result = self._validate_file(file_path)
                result.errors.extend(file_result.errors)
                result.warnings.extend(file_result.warnings)
                result.fixes_applied.extend(file_result.fixes_applied)
                if not file_result.is_valid:
                    result.is_valid = False
        
        # Step 2: Check consistency across files
        consistency_result = self._check_consistency()
        result.errors.extend(consistency_result.errors)
        result.warnings.extend(consistency_result.warnings)
        result.fixes_applied.extend(consistency_result.fixes_applied)
        if not consistency_result.is_valid:
            result.is_valid = False
        
        # Step 3: Validate template files exist
        template_result = self._validate_templates()
        result.errors.extend(template_result.errors)
        result.warnings.extend(template_result.warnings)
        result.fixes_applied.extend(template_result.fixes_applied)
        if not template_result.is_valid:
            result.is_valid = False
        
        # Step 4: Test installation in isolated environment
        if result.is_valid:
            install_result = self._test_installation()
            result.errors.extend(install_result.errors)
            result.warnings.extend(install_result.warnings)
            if not install_result.is_valid:
                result.is_valid = False
        
        return result

    def _validate_file(self, file_path: Path) -> ValidationResult:
        """Validate a single requirements file"""
        result = ValidationResult(True, [], [], [])
        
        if not file_path.exists():
            result.errors.append(f"Requirements file not found: {file_path}")
            result.is_valid = False
            return result
        
        print(f"  📄 Validating {file_path.name}...")
        
        packages = self._parse_requirements_file(file_path)
        modified = False
        
        for package in packages:
            # Check for package name corrections
            if package.name in self.package_corrections:
                correction = self.package_corrections[package.name]
                if correction is None:
                    # Remove deprecated package
                    self._comment_out_line(file_path, package.line_number)
                    result.fixes_applied.append(f"Commented out deprecated package: {package.name}")
                    modified = True
                else:
                    # Correct package name
                    self._fix_package_name(file_path, package, correction)
                    result.fixes_applied.append(f"Fixed package name: {package.name} → {correction}")
                    modified = True
            
            # Check for version compatibility issues
            compatibility_fixes = self._check_compatibility(package)
            if compatibility_fixes:
                for fix in compatibility_fixes:
                    self._apply_version_fix(file_path, package, fix)
                    result.fixes_applied.append(f"Applied compatibility fix: {fix}")
                    modified = True
        
        if modified:
            result.warnings.append(f"Modified {file_path.name} to fix dependency issues")
        
        return result

    def _parse_requirements_file(self, file_path: Path) -> List[PackageInfo]:
        """Parse a requirements file and extract package information"""
        packages = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#') or line.startswith('-r'):
                continue
            
            # Parse package==version format
            match = re.match(r'^([a-zA-Z0-9_-]+)([><=!~]+)([0-9.]+.*?)(\s*#.*)?$', line)
            if match:
                name = match.group(1)
                version = match.group(3)
                comment = match.group(4) or ""
                
                packages.append(PackageInfo(
                    name=name,
                    version=version,
                    original_line=line,
                    line_number=line_num,
                    file_path=str(file_path),
                    comment=comment
                ))
        
        return packages

    def _check_compatibility(self, package: PackageInfo) -> List[str]:
        """Check if package version is compatible with known constraints"""
        fixes = []

        for constraint_package, constraints in self.compatibility_constraints.items():
            if package.name in constraints:
                required_version = constraints[package.name]
                if not self._version_satisfies(package.version, required_version):
                    fixes.append(f"{package.name}{required_version}")

        return fixes

    def _version_satisfies(self, current: str, required: str) -> bool:
        """Check if current version satisfies required constraint"""
        # Simple version checking - can be enhanced with packaging library
        if required.startswith("~="):
            # Compatible release
            base_version = required[2:]
            return current.startswith(base_version.rsplit('.', 1)[0])
        elif required.startswith("<="):
            # Less than or equal
            max_version = required[2:]
            return self._compare_versions(current, max_version) <= 0
        elif required.startswith("<"):
            # Less than
            max_version = required[1:]
            return self._compare_versions(current, max_version) < 0
        elif required.startswith(">="):
            # Greater than or equal
            min_version = required[2:]
            return self._compare_versions(current, min_version) >= 0
        elif "," in required:
            # Multiple constraints
            constraints = [c.strip() for c in required.split(",")]
            return all(self._version_satisfies(current, c) for c in constraints)

        return True

    def _compare_versions(self, v1: str, v2: str) -> int:
        """Compare two version strings. Returns -1, 0, or 1"""
        def normalize(v):
            return [int(x) for x in re.sub(r'[^\d.]', '', v).split('.') if x.isdigit()]

        v1_parts = normalize(v1)
        v2_parts = normalize(v2)

        # Pad with zeros to make same length
        max_len = max(len(v1_parts), len(v2_parts))
        v1_parts.extend([0] * (max_len - len(v1_parts)))
        v2_parts.extend([0] * (max_len - len(v2_parts)))

        for a, b in zip(v1_parts, v2_parts):
            if a < b:
                return -1
            elif a > b:
                return 1
        return 0

    def _fix_package_name(self, file_path: Path, package: PackageInfo, new_name: str):
        """Fix incorrect package name in requirements file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Replace the package name in the specific line
        old_line = lines[package.line_number - 1]
        new_line = old_line.replace(package.name, new_name, 1)
        lines[package.line_number - 1] = new_line

        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)

    def _apply_version_fix(self, file_path: Path, package: PackageInfo, fix: str):
        """Apply version fix to requirements file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Replace the entire package specification
        old_line = lines[package.line_number - 1]
        # Preserve comment if it exists
        comment_match = re.search(r'(\s*#.*)', old_line)
        comment = comment_match.group(1) if comment_match else ""

        new_line = f"{fix}{comment}\n"
        lines[package.line_number - 1] = new_line

        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)

    def _comment_out_line(self, file_path: Path, line_number: int):
        """Comment out a line in requirements file"""
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        old_line = lines[line_number - 1]
        if not old_line.strip().startswith('#'):
            lines[line_number - 1] = f"# {old_line}"

        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(lines)

    def _check_consistency(self) -> ValidationResult:
        """Check consistency of core packages across all requirements files"""
        result = ValidationResult(True, [], [], [])

        print("  🔄 Checking consistency across files...")

        # Parse all files
        all_packages = {}
        for req_file in self.requirements_files:
            file_path = self.project_root / req_file
            if file_path.exists():
                packages = self._parse_requirements_file(file_path)
                all_packages[req_file] = {p.name: p for p in packages}

        # Check core packages for version consistency
        for package_name in self.core_packages:
            versions = {}
            for file_name, packages in all_packages.items():
                if package_name in packages:
                    versions[file_name] = packages[package_name].version

            if len(set(versions.values())) > 1:
                result.warnings.append(
                    f"Version inconsistency for {package_name}: {versions}"
                )

        return result

    def _validate_templates(self) -> ValidationResult:
        """Ensure required template files exist"""
        result = ValidationResult(True, [], [], [])

        print("  📋 Validating template files...")

        # Check for .env template
        env_example = self.project_root / ".env.example"
        env_template = self.project_root / "src/kitco_research_ai/defaults/.env.template"

        if env_example.exists() and not env_template.exists():
            # Copy .env.example to expected location
            env_template.parent.mkdir(parents=True, exist_ok=True)
            import shutil
            shutil.copy2(env_example, env_template)
            result.fixes_applied.append("Created .env.template from .env.example")

        return result

    def _test_installation(self) -> ValidationResult:
        """Test installation in isolated environment"""
        result = ValidationResult(True, [], [], [])

        print("  🧪 Testing installation in isolated environment...")

        # Create temporary virtual environment
        with tempfile.TemporaryDirectory() as temp_dir:
            venv_path = Path(temp_dir) / "test_venv"

            try:
                # Create virtual environment
                subprocess.run([
                    sys.executable, "-m", "venv", str(venv_path)
                ], check=True, capture_output=True)

                # Get pip path
                if os.name == 'nt':
                    pip_path = venv_path / "Scripts" / "pip.exe"
                else:
                    pip_path = venv_path / "bin" / "pip"

                # Test installation of core requirements
                core_req_file = self.project_root / "config/requirements.txt"
                if core_req_file.exists():
                    install_result = subprocess.run([
                        str(pip_path), "install", "--dry-run", "-r", str(core_req_file)
                    ], capture_output=True, text=True, timeout=300)

                    if install_result.returncode != 0:
                        result.errors.append(
                            f"Installation test failed: {install_result.stderr}"
                        )
                        result.is_valid = False
                    else:
                        print("    ✅ Installation test passed")

            except subprocess.TimeoutExpired:
                result.warnings.append("Installation test timed out")
            except Exception as e:
                result.warnings.append(f"Installation test failed: {e}")

        return result

    def create_backup(self):
        """Create backup of requirements files before making changes"""
        backup_dir = self.project_root / "config" / "backups"
        backup_dir.mkdir(exist_ok=True)

        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

        for req_file in self.requirements_files:
            file_path = self.project_root / req_file
            if file_path.exists():
                backup_path = backup_dir / f"{file_path.name}.{timestamp}.bak"
                import shutil
                shutil.copy2(file_path, backup_path)
                print(f"  💾 Backed up {req_file} to {backup_path}")

    def generate_report(self, result: ValidationResult) -> str:
        """Generate a detailed validation report"""
        report = []
        report.append("=" * 60)
        report.append("DEPENDENCY VALIDATION REPORT")
        report.append("=" * 60)

        if result.is_valid:
            report.append("✅ VALIDATION PASSED")
        else:
            report.append("❌ VALIDATION FAILED")

        if result.fixes_applied:
            report.append("\n🔧 FIXES APPLIED:")
            for fix in result.fixes_applied:
                report.append(f"  • {fix}")

        if result.warnings:
            report.append("\n⚠️  WARNINGS:")
            for warning in result.warnings:
                report.append(f"  • {warning}")

        if result.errors:
            report.append("\n❌ ERRORS:")
            for error in result.errors:
                report.append(f"  • {error}")

        report.append("\n" + "=" * 60)
        return "\n".join(report)


def main():
    """Main entry point"""
    try:
        # Find project root
        current_dir = Path.cwd()
        project_root = current_dir

        # Search for project root
        while project_root != project_root.parent:
            if (project_root / "app.py").exists() and (project_root / "src/kitco_research_ai").exists():
                break
            project_root = project_root.parent
        else:
            print("❌ Could not find project root directory")
            return 1

        print(f"📁 Project root: {project_root}")

        # Create validator
        validator = DependencyValidator(project_root)

        # Create backup
        validator.create_backup()

        # Run validation
        result = validator.validate_all()

        # Generate and display report
        report = validator.generate_report(result)
        print("\n" + report)

        # Save report to file
        report_file = project_root / "config" / "dependency_validation_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"\n📄 Report saved to: {report_file}")

        return 0 if result.is_valid else 1

    except Exception as e:
        print(f"❌ Validation failed with error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
