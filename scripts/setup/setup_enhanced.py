#!/usr/bin/env python3
"""
Kitco Research AI - Enhanced Cross-Platform Setup Script
========================================================
This script provides a robust, cross-platform setup with automatic dependency validation,
conflict resolution, and comprehensive error handling to prevent setup failures.
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
from typing import Optional, List, Tuple
import argparse
import json
import tempfile

# Import our dependency validator
sys.path.insert(0, str(Path(__file__).parent))
from dependency_validator import DependencyValidator, ValidationResult


class EnhancedSetup:
    """Enhanced setup with automatic dependency validation and fixing"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.platform = platform.system().lower()
        self.python_executable = sys.executable
        self.venv_path = project_root / ".venv"
        
        # Platform-specific configurations
        self.platform_configs = {
            "windows": {
                "venv_python": self.venv_path / "Scripts" / "python.exe",
                "venv_pip": self.venv_path / "Scripts" / "pip.exe",
                "activate_script": self.venv_path / "Scripts" / "activate.bat",
            },
            "darwin": {
                "venv_python": self.venv_path / "bin" / "python",
                "venv_pip": self.venv_path / "bin" / "pip",
                "activate_script": self.venv_path / "bin" / "activate",
            },
            "linux": {
                "venv_python": self.venv_path / "bin" / "python",
                "venv_pip": self.venv_path / "bin" / "pip",
                "activate_script": self.venv_path / "bin" / "activate",
            }
        }

    def run_setup(self, environment: str = "development", force: bool = False) -> bool:
        """Run the complete setup process"""
        print("🔬 Kitco Research AI - Enhanced Setup")
        print("=" * 50)
        print(f"🖥️  Platform: {platform.system()}")
        print(f"🐍 Python: {sys.version}")
        print(f"📁 Project Root: {self.project_root}")
        print()
        
        try:
            # Step 1: Validate and fix dependencies
            if not self._validate_dependencies():
                print("❌ Dependency validation failed")
                return False
            
            # Step 2: Check prerequisites
            if not self._check_prerequisites():
                print("❌ Prerequisites check failed")
                return False
            
            # Step 3: Create/validate virtual environment
            if not self._setup_virtual_environment(force):
                print("❌ Virtual environment setup failed")
                return False
            
            # Step 4: Install dependencies
            if not self._install_dependencies(environment):
                print("❌ Dependency installation failed")
                return False
            
            # Step 5: Install Playwright browsers
            if not self._install_playwright_browsers():
                print("❌ Playwright browser installation failed")
                return False
            
            # Step 6: Setup configuration
            if not self._setup_configuration():
                print("❌ Configuration setup failed")
                return False
            
            # Step 7: Initialize database
            if not self._initialize_database():
                print("❌ Database initialization failed")
                return False
            
            # Step 8: Final validation
            if not self._final_validation():
                print("❌ Final validation failed")
                return False
            
            print("\n🎉 Setup completed successfully!")
            self._print_next_steps()
            return True
            
        except KeyboardInterrupt:
            print("\n⚠️  Setup interrupted by user")
            return False
        except Exception as e:
            print(f"\n❌ Setup failed with error: {e}")
            return False

    def _validate_dependencies(self) -> bool:
        """Validate and fix dependency issues"""
        print("🔍 Validating dependencies...")
        
        validator = DependencyValidator(self.project_root)
        
        # Create backup before making changes
        validator.create_backup()
        
        # Run validation
        result = validator.validate_all()
        
        if result.fixes_applied:
            print("🔧 Applied dependency fixes:")
            for fix in result.fixes_applied:
                print(f"  • {fix}")
        
        if result.warnings:
            print("⚠️  Warnings:")
            for warning in result.warnings:
                print(f"  • {warning}")
        
        if not result.is_valid:
            print("❌ Dependency validation failed:")
            for error in result.errors:
                print(f"  • {error}")
            return False
        
        print("✅ Dependencies validated successfully")
        return True

    def _check_prerequisites(self) -> bool:
        """Check system prerequisites"""
        print("🔍 Checking prerequisites...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print(f"❌ Python 3.8+ required, found {sys.version}")
            return False
        print(f"✅ Python version: {sys.version.split()[0]}")
        
        # Check pip
        try:
            subprocess.run([self.python_executable, "-m", "pip", "--version"], 
                         check=True, capture_output=True)
            print("✅ pip is available")
        except subprocess.CalledProcessError:
            print("❌ pip is not available")
            return False
        
        # Check project structure
        required_paths = [
            "app.py",
            "src/kitco_research_ai",
            "config/requirements.txt",
            "scripts"
        ]
        
        for path in required_paths:
            if not (self.project_root / path).exists():
                print(f"❌ Missing required path: {path}")
                return False
        print("✅ Project structure validated")
        
        return True

    def _setup_virtual_environment(self, force: bool) -> bool:
        """Create or validate virtual environment"""
        print("🐍 Setting up virtual environment...")
        
        if self.venv_path.exists():
            if force:
                print("🗑️  Removing existing virtual environment...")
                shutil.rmtree(self.venv_path)
            else:
                print("✅ Virtual environment already exists")
                return self._validate_venv()
        
        # Create virtual environment
        try:
            subprocess.run([
                self.python_executable, "-m", "venv", str(self.venv_path)
            ], check=True, capture_output=True)
            print("✅ Virtual environment created")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            return False

    def _validate_venv(self) -> bool:
        """Validate existing virtual environment"""
        config = self.platform_configs.get(self.platform, self.platform_configs["linux"])
        
        if not config["venv_python"].exists():
            print("❌ Virtual environment Python not found")
            return False
        
        if not config["venv_pip"].exists():
            print("❌ Virtual environment pip not found")
            return False
        
        return True

    def _install_dependencies(self, environment: str) -> bool:
        """Install Python dependencies"""
        print("📦 Installing dependencies...")
        
        # Determine requirements file
        req_files = {
            "development": "config/requirements-dev.txt",
            "production": "config/requirements-prod.txt",
            "standard": "config/requirements.txt"
        }
        
        req_file = req_files.get(environment, "config/requirements-dev.txt")
        req_path = self.project_root / req_file
        
        if not req_path.exists():
            print(f"❌ Requirements file not found: {req_file}")
            return False
        
        config = self.platform_configs.get(self.platform, self.platform_configs["linux"])
        
        # Upgrade pip first
        try:
            subprocess.run([
                str(config["venv_pip"]), "install", "--upgrade", "pip"
            ], check=True, capture_output=True)
            print("✅ pip upgraded")
        except subprocess.CalledProcessError as e:
            print(f"⚠️  pip upgrade failed: {e}")
        
        # Install dependencies with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"  📄 Installing from {req_file} (attempt {attempt + 1}/{max_retries})...")
                result = subprocess.run([
                    str(config["venv_pip"]), "install", "-r", str(req_path)
                ], check=True, capture_output=True, text=True, timeout=1800)
                
                print("✅ Dependencies installed successfully")
                return True
                
            except subprocess.TimeoutExpired:
                print(f"⚠️  Installation timed out on attempt {attempt + 1}")
                if attempt == max_retries - 1:
                    print("❌ Installation failed after all retries")
                    return False
            except subprocess.CalledProcessError as e:
                print(f"⚠️  Installation failed on attempt {attempt + 1}: {e.stderr}")
                if attempt == max_retries - 1:
                    print("❌ Installation failed after all retries")
                    return False
        
        return False

    def _install_playwright_browsers(self) -> bool:
        """Install Playwright browsers"""
        print("🌐 Installing Playwright browsers...")

        config = self.platform_configs.get(self.platform, self.platform_configs["linux"])

        try:
            subprocess.run([
                str(config["venv_python"]), "-m", "playwright", "install"
            ], check=True, capture_output=True, timeout=600)
            print("✅ Playwright browsers installed")
            return True
        except subprocess.TimeoutExpired:
            print("⚠️  Playwright installation timed out, but continuing...")
            return True  # Don't fail setup for this
        except subprocess.CalledProcessError as e:
            print(f"⚠️  Playwright installation failed: {e}")
            return True  # Don't fail setup for this

    def _setup_configuration(self) -> bool:
        """Setup configuration files"""
        print("⚙️  Setting up configuration...")

        # Ensure .env file exists
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        env_template = self.project_root / "src/kitco_research_ai/defaults/.env.template"

        if not env_file.exists():
            if env_template.exists():
                shutil.copy2(env_template, env_file)
                print("✅ Created .env from template")
            elif env_example.exists():
                shutil.copy2(env_example, env_file)
                print("✅ Created .env from example")
            else:
                print("⚠️  No .env template found, creating basic .env")
                self._create_basic_env_file(env_file)
        else:
            print("✅ .env file already exists")

        # Ensure template file exists in expected location
        if env_example.exists() and not env_template.exists():
            env_template.parent.mkdir(parents=True, exist_ok=True)
            shutil.copy2(env_example, env_template)
            print("✅ Created .env.template in expected location")

        return True

    def _create_basic_env_file(self, env_file: Path):
        """Create a basic .env file with essential settings"""
        basic_env = """# Kitco Research AI - Basic Configuration
SECRET_KEY=change-this-secret-key-in-production
ENVIRONMENT=development
DEBUG=true
HOST=0.0.0.0
PORT=8765

# Add your API keys here:
# OPENAI_API_KEY=your-openai-api-key
# SERPAPI_API_KEY=your-serpapi-key
"""
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(basic_env)

    def _initialize_database(self) -> bool:
        """Initialize the database"""
        print("🗄️  Initializing database...")

        config = self.platform_configs.get(self.platform, self.platform_configs["linux"])
        setup_script = self.project_root / "src/kitco_research_ai/setup_data_dir.py"

        if setup_script.exists():
            try:
                subprocess.run([
                    str(config["venv_python"]), str(setup_script)
                ], check=True, capture_output=True, cwd=self.project_root)
                print("✅ Database initialized")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ Database initialization failed: {e}")
                return False
        else:
            print("⚠️  Database setup script not found, skipping...")
            return True

    def _final_validation(self) -> bool:
        """Perform final validation"""
        print("🔍 Running final validation...")

        config = self.platform_configs.get(self.platform, self.platform_configs["linux"])

        # Test critical imports
        test_imports = [
            "flask", "langchain", "playwright", "sqlalchemy",
            "requests", "beautifulsoup4", "pydantic"
        ]

        for module in test_imports:
            try:
                subprocess.run([
                    str(config["venv_python"]), "-c", f"import {module}"
                ], check=True, capture_output=True)
            except subprocess.CalledProcessError:
                print(f"❌ Failed to import {module}")
                return False

        print("✅ All critical modules can be imported")

        # Test basic app startup (dry run)
        app_file = self.project_root / "app.py"
        if app_file.exists():
            try:
                result = subprocess.run([
                    str(config["venv_python"]), "-c",
                    "import sys; sys.path.insert(0, '.'); "
                    "from src.kitco_research_ai.web.app_factory import create_app; "
                    "app = create_app(); print('App creation successful')"
                ], check=True, capture_output=True, text=True,
                cwd=self.project_root, timeout=30)
                print("✅ App creation test passed")
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
                print(f"⚠️  App creation test failed: {e}")
                # Don't fail setup for this

        return True

    def _print_next_steps(self):
        """Print next steps for the user"""
        print("\n" + "=" * 50)
        print("🎉 SETUP COMPLETE!")
        print("=" * 50)

        activate_cmd = {
            "windows": ".venv\\Scripts\\activate",
            "darwin": "source .venv/bin/activate",
            "linux": "source .venv/bin/activate"
        }.get(self.platform, "source .venv/bin/activate")

        print("\n🚀 Next steps:")
        print(f"1. Activate virtual environment: {activate_cmd}")
        print("2. Edit .env file to add your API keys")
        print("3. Start the application: ./scripts/start_app.sh")
        print("4. Open http://localhost:8765 in your browser")

        print("\n🔧 Development commands:")
        print("  pytest                    # Run tests")
        print("  black src/                # Format code")
        print("  mypy src/                 # Type checking")
        print("  pip-audit                 # Security audit")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Enhanced Kitco Research AI Setup")
    parser.add_argument(
        "command",
        choices=["setup", "validate", "fix-deps"],
        help="Command to run"
    )
    parser.add_argument(
        "--environment", "-e",
        choices=["development", "production", "standard"],
        default="development",
        help="Environment to set up"
    )
    parser.add_argument(
        "--force", "-f",
        action="store_true",
        help="Force recreation of virtual environment"
    )

    args = parser.parse_args()

    # Find project root
    current_dir = Path.cwd()
    project_root = current_dir

    while project_root != project_root.parent:
        if (project_root / "app.py").exists() and (project_root / "src/kitco_research_ai").exists():
            break
        project_root = project_root.parent
    else:
        print("❌ Could not find project root directory")
        return 1

    setup = EnhancedSetup(project_root)

    if args.command == "setup":
        success = setup.run_setup(args.environment, args.force)
        return 0 if success else 1
    elif args.command == "validate":
        validator = DependencyValidator(project_root)
        result = validator.validate_all()
        print(validator.generate_report(result))
        return 0 if result.is_valid else 1
    elif args.command == "fix-deps":
        validator = DependencyValidator(project_root)
        validator.create_backup()
        result = validator.validate_all()
        print(validator.generate_report(result))
        return 0 if result.is_valid else 1


if __name__ == "__main__":
    sys.exit(main())
