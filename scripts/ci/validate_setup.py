#!/usr/bin/env python3
"""
Kitco Research AI - CI/CD Setup Validation
==========================================
This script validates that the setup process works correctly across all platforms.
It should be run in CI/CD pipelines to prevent setup issues from reaching users.
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import json
import platform


class SetupValidator:
    """Validates setup process across different environments"""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.platform = platform.system().lower()
        self.python_versions = ["3.8", "3.9", "3.10", "3.11", "3.12"]
        
    def validate_all_environments(self) -> bool:
        """Validate setup across all supported environments"""
        print("🔍 CI/CD Setup Validation")
        print("=" * 50)
        
        results = {}
        overall_success = True
        
        # Test current Python version
        current_python = f"{sys.version_info.major}.{sys.version_info.minor}"
        print(f"🐍 Testing with Python {current_python}")
        
        # Test different environment types
        environments = ["development", "production", "standard"]
        
        for env in environments:
            print(f"\n📦 Testing {env} environment...")
            success = self._test_environment(env)
            results[env] = success
            if not success:
                overall_success = False
                print(f"❌ {env} environment failed")
            else:
                print(f"✅ {env} environment passed")
        
        # Test dependency validation
        print(f"\n🔍 Testing dependency validation...")
        dep_success = self._test_dependency_validation()
        results["dependency_validation"] = dep_success
        if not dep_success:
            overall_success = False
            print("❌ Dependency validation failed")
        else:
            print("✅ Dependency validation passed")
        
        # Generate report
        self._generate_ci_report(results)
        
        return overall_success
    
    def _test_environment(self, environment: str) -> bool:
        """Test setup for a specific environment"""
        with tempfile.TemporaryDirectory() as temp_dir:
            test_dir = Path(temp_dir) / "test_setup"
            
            try:
                # Copy project to test directory
                shutil.copytree(self.project_root, test_dir, 
                              ignore=shutil.ignore_patterns('.venv', '__pycache__', '*.pyc'))
                
                # Run enhanced setup
                setup_script = test_dir / "scripts/setup/setup_enhanced.py"
                if not setup_script.exists():
                    print(f"⚠️  Enhanced setup script not found, using fallback")
                    setup_script = test_dir / "scripts/setup/setup_cross_platform.py"
                
                if not setup_script.exists():
                    print(f"❌ No setup script found")
                    return False
                
                # Run setup
                result = subprocess.run([
                    sys.executable, str(setup_script), "setup", 
                    "--environment", environment
                ], cwd=test_dir, capture_output=True, text=True, timeout=1800)
                
                if result.returncode != 0:
                    print(f"❌ Setup failed: {result.stderr}")
                    return False
                
                # Test that virtual environment was created
                venv_path = test_dir / ".venv"
                if not venv_path.exists():
                    print("❌ Virtual environment not created")
                    return False
                
                # Test that key packages can be imported
                if self.platform == "windows":
                    python_exe = venv_path / "Scripts" / "python.exe"
                else:
                    python_exe = venv_path / "bin" / "python"
                
                test_imports = ["flask", "langchain", "requests", "pydantic"]
                for module in test_imports:
                    import_result = subprocess.run([
                        str(python_exe), "-c", f"import {module}"
                    ], capture_output=True)
                    
                    if import_result.returncode != 0:
                        print(f"❌ Failed to import {module}")
                        return False
                
                return True
                
            except subprocess.TimeoutExpired:
                print("❌ Setup timed out")
                return False
            except Exception as e:
                print(f"❌ Setup test failed: {e}")
                return False
    
    def _test_dependency_validation(self) -> bool:
        """Test the dependency validation system"""
        try:
            # Import and test dependency validator
            sys.path.insert(0, str(self.project_root / "scripts/setup"))
            from dependency_validator import DependencyValidator
            
            validator = DependencyValidator(self.project_root)
            result = validator.validate_all()
            
            if not result.is_valid:
                print("❌ Dependency validation found issues:")
                for error in result.errors:
                    print(f"  • {error}")
                return False
            
            return True
            
        except ImportError as e:
            print(f"❌ Could not import dependency validator: {e}")
            return False
        except Exception as e:
            print(f"❌ Dependency validation failed: {e}")
            return False
    
    def _generate_ci_report(self, results: Dict[str, bool]):
        """Generate CI/CD report"""
        report_data = {
            "platform": platform.system(),
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}",
            "timestamp": subprocess.run(["date"], capture_output=True, text=True).stdout.strip(),
            "results": results,
            "overall_success": all(results.values())
        }
        
        # Save JSON report
        report_file = self.project_root / "ci_validation_report.json"
        with open(report_file, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        # Print summary
        print("\n" + "=" * 50)
        print("CI/CD VALIDATION SUMMARY")
        print("=" * 50)
        
        for env, success in results.items():
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"{env:25} {status}")
        
        overall = "✅ PASS" if report_data["overall_success"] else "❌ FAIL"
        print(f"{'OVERALL':25} {overall}")
        
        print(f"\n📄 Report saved to: {report_file}")


def main():
    """Main entry point for CI/CD validation"""
    # Find project root
    current_dir = Path.cwd()
    project_root = current_dir
    
    while project_root != project_root.parent:
        if (project_root / "app.py").exists() and (project_root / "src/kitco_research_ai").exists():
            break
        project_root = project_root.parent
    else:
        print("❌ Could not find project root directory")
        return 1
    
    validator = SetupValidator(project_root)
    success = validator.validate_all_environments()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
