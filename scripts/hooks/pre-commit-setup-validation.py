#!/usr/bin/env python3
"""
Kitco Research AI - Pre-commit Setup Validation Hook
====================================================
This script runs as a pre-commit hook to validate that any changes to
dependency files don't break the setup process.
"""

import os
import sys
import subprocess
from pathlib import Path
import tempfile
import shutil


def get_changed_files():
    """Get list of changed files in the current commit"""
    try:
        result = subprocess.run([
            "git", "diff", "--cached", "--name-only"
        ], capture_output=True, text=True, check=True)
        return result.stdout.strip().split('\n') if result.stdout.strip() else []
    except subprocess.CalledProcessError:
        return []


def should_validate_setup(changed_files):
    """Check if setup validation is needed based on changed files"""
    setup_related_patterns = [
        'config/requirements',
        'scripts/setup/',
        '.env.example',
        'app.py',
        'src/kitco_research_ai/setup_data_dir.py'
    ]
    
    for file_path in changed_files:
        for pattern in setup_related_patterns:
            if pattern in file_path:
                return True
    return False


def validate_dependencies(project_root):
    """Validate dependencies using the dependency validator"""
    try:
        sys.path.insert(0, str(project_root / "scripts/setup"))
        from dependency_validator import DependencyValidator
        
        validator = DependencyValidator(project_root)
        result = validator.validate_all()
        
        if not result.is_valid:
            print("❌ Dependency validation failed:")
            for error in result.errors:
                print(f"  • {error}")
            return False
        
        if result.fixes_applied:
            print("🔧 Dependency fixes were applied:")
            for fix in result.fixes_applied:
                print(f"  • {fix}")
            print("\n⚠️  Please review and stage the fixed files before committing.")
            return False
        
        if result.warnings:
            print("⚠️  Dependency warnings:")
            for warning in result.warnings:
                print(f"  • {warning}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import dependency validator: {e}")
        return False
    except Exception as e:
        print(f"❌ Dependency validation failed: {e}")
        return False


def quick_setup_test(project_root):
    """Perform a quick setup test in an isolated environment"""
    print("🧪 Running quick setup test...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "test_setup"
        
        try:
            # Copy only essential files for testing
            essential_files = [
                "config/requirements.txt",
                "config/requirements-dev.txt", 
                "scripts/setup/",
                ".env.example",
                "src/kitco_research_ai/defaults/"
            ]
            
            test_dir.mkdir()
            for file_pattern in essential_files:
                source_path = project_root / file_pattern
                if source_path.exists():
                    if source_path.is_dir():
                        shutil.copytree(source_path, test_dir / file_pattern)
                    else:
                        dest_path = test_dir / file_pattern
                        dest_path.parent.mkdir(parents=True, exist_ok=True)
                        shutil.copy2(source_path, dest_path)
            
            # Test dependency parsing
            setup_script = test_dir / "scripts/setup/dependency_validator.py"
            if setup_script.exists():
                result = subprocess.run([
                    sys.executable, str(setup_script)
                ], cwd=test_dir, capture_output=True, text=True, timeout=60)
                
                if result.returncode != 0:
                    print(f"❌ Quick setup test failed: {result.stderr}")
                    return False
            
            print("✅ Quick setup test passed")
            return True
            
        except subprocess.TimeoutExpired:
            print("❌ Quick setup test timed out")
            return False
        except Exception as e:
            print(f"❌ Quick setup test failed: {e}")
            return False


def main():
    """Main pre-commit hook entry point"""
    # Find project root
    current_dir = Path.cwd()
    project_root = current_dir
    
    while project_root != project_root.parent:
        if (project_root / ".git").exists():
            break
        project_root = project_root.parent
    else:
        print("❌ Could not find git repository root")
        return 1
    
    # Check if we're in the right project
    if not (project_root / "app.py").exists():
        print("❌ This doesn't appear to be the Kitco Research AI project")
        return 1
    
    print("🔍 Pre-commit Setup Validation")
    print("=" * 40)
    
    # Get changed files
    changed_files = get_changed_files()
    if not changed_files:
        print("ℹ️  No files changed, skipping validation")
        return 0
    
    # Check if setup validation is needed
    if not should_validate_setup(changed_files):
        print("ℹ️  No setup-related files changed, skipping validation")
        return 0
    
    print("📝 Setup-related files changed:")
    for file_path in changed_files:
        if any(pattern in file_path for pattern in ['config/requirements', 'scripts/setup/', '.env']):
            print(f"  • {file_path}")
    
    # Run dependency validation
    print("\n🔍 Validating dependencies...")
    if not validate_dependencies(project_root):
        print("\n❌ Pre-commit validation failed!")
        print("Please fix the dependency issues before committing.")
        return 1
    
    # Run quick setup test
    if not quick_setup_test(project_root):
        print("\n❌ Pre-commit validation failed!")
        print("Please fix the setup issues before committing.")
        return 1
    
    print("\n✅ Pre-commit validation passed!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
